import sys
import os
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QDockWidget, QListWidget, QWidget, QVBoxLayout, QLabel, QMessageBox
from ui.login_dialog import LoginDialog
from db.database import Database
from utils.config import DB_PATH
import pyodbc

class MainWindow(QMainWindow):
    def __init__(self, user):
        super().__init__()
        self.setWindowTitle('Authentication and User Management System')
        self.setGeometry(100, 100, 900, 600)
        self.user = user
        # Sidebar navigation
        self.sidebar = QListWidget()
        self.sidebar.addItems([
            'User & Permissions Management',
            'Inventory Management',
            'Recipe & Production Management',
            'Sample Testing & Quality Assurance',
            'Report & Document Creation',
            'Production & Quality Management',
            'Settings'
        ])
        self.sidebar.currentRowChanged.connect(self.display_section)
        dock = QDockWidget('Main Menu', self)
        dock.setWidget(self.sidebar)
        dock.setFeatures(QDockWidget.NoDockWidgetFeatures)
        self.addDockWidget(1, dock)  # 1 = LeftDockWidgetArea

        # Central widget placeholder
        self.central = QWidget()
        self.layout = QVBoxLayout()
        self.label = QLabel(f'Welcome, {self.user[1]}! Select a section from the menu.')
        self.layout.addWidget(self.label)
        self.central.setLayout(self.layout)
        self.setCentralWidget(self.central)

    def display_section(self, index):
        sections = [
            'User & Permissions Management',
            'Inventory Management',
            'Recipe & Production Management',
            'Sample Testing & Quality Assurance',
            'Report & Document Creation',
            'Production & Quality Management',
            'Settings'
        ]
        if 0 <= index < len(sections):
            self.label.setText(f'You selected: {sections[index]}')
        else:
            self.label.setText('Select a section from the menu.')

def get_users_from_db():
    print(f"Attempting to connect to database at: {DB_PATH}")
    print(f"Database file exists: {os.path.exists(DB_PATH)}")
    
    db = Database(DB_PATH)
    try:
        conn = db.connect()
        cursor = conn.cursor()
        try:
            print("Executing SQL query...")
            cursor.execute('SELECT id, username, password, role FROM Users')
            users = cursor.fetchall()
            if not users:
                print("No users found in the database!")
            else:
                print(f"Found {len(users)} users")
            return users
        except Exception as e:
            error_msg = f'Error reading Users table: {str(e)}'
            print(error_msg)
            QMessageBox.critical(None, 'Database Error', error_msg)
            return None
        finally:
            db.close()
    except Exception as e:
        error_msg = f'Error connecting to database: {str(e)}'
        print(error_msg)
        QMessageBox.critical(None, 'Database Error', error_msg)
        return None

if __name__ == '__main__':
    print("Starting application...")
    app = QApplication(sys.argv)
    
    # Check if database file exists
    if not os.path.exists(DB_PATH):
        error_msg = f"Database file not found at: {DB_PATH}"
        print(error_msg)
        QMessageBox.critical(None, 'Database Error', error_msg)
        sys.exit(1)
        
    users = get_users_from_db()
    if not users:
        sys.exit(1)
    
    login = LoginDialog(users)
    if login.exec_() == LoginDialog.Accepted:
        user = next((u for u in users if u[1] == login.username_input.text()), None)
        window = MainWindow(user)
        window.show()
        sys.exit(app.exec_())
