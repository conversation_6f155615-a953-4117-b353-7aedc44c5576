"""
Database creation and initialization script for the Authentication and User Management System.
This script creates all necessary tables and populates them with initial data.
"""

import pyodbc
import os
import hashlib
from datetime import datetime

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_database_schema(db_path):
    """Create the complete database schema"""
    
    # Ensure the database file exists
    if not os.path.exists(db_path):
        # Create an empty Access database file
        print(f"Creating new database file at: {db_path}")
        # Note: This requires Access to be installed or use alternative method
        
    try:
        connection_string = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={os.path.abspath(db_path)}'
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        print("Creating database schema...")
        
        # Drop existing tables if they exist (for clean setup)
        tables_to_drop = [
            'TestSamples', 'ProductionBatches', 'RecipeIngredients', 
            'Recipes', 'Inventory', 'UserSessions', 'Users'
        ]
        
        for table in tables_to_drop:
            try:
                cursor.execute(f"DROP TABLE {table}")
                print(f"Dropped existing table: {table}")
            except:
                pass  # Table doesn't exist, continue
        
        # Create Users table
        cursor.execute("""
            CREATE TABLE Users (
                id AUTOINCREMENT PRIMARY KEY,
                username TEXT(50) NOT NULL UNIQUE,
                password_hash TEXT(64) NOT NULL,
                role TEXT(20) NOT NULL,
                full_name TEXT(100),
                email TEXT(100),
                created_date DATETIME DEFAULT NOW(),
                last_login DATETIME,
                is_active YESNO DEFAULT TRUE
            )
        """)
        print("Created Users table")
        
        # Create UserSessions table for session management
        cursor.execute("""
            CREATE TABLE UserSessions (
                session_id TEXT(64) PRIMARY KEY,
                user_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT NOW(),
                expires_at DATETIME NOT NULL,
                is_active YESNO DEFAULT TRUE,
                FOREIGN KEY (user_id) REFERENCES Users(id)
            )
        """)
        print("Created UserSessions table")
        
        # Create Inventory table
        cursor.execute("""
            CREATE TABLE Inventory (
                item_id AUTOINCREMENT PRIMARY KEY,
                name TEXT(100) NOT NULL,
                description TEXT(255),
                quantity DOUBLE DEFAULT 0,
                unit TEXT(20) NOT NULL,
                minimum_stock DOUBLE DEFAULT 0,
                cost_per_unit CURRENCY DEFAULT 0,
                supplier TEXT(100),
                category TEXT(50),
                created_date DATETIME DEFAULT NOW(),
                last_updated DATETIME DEFAULT NOW()
            )
        """)
        print("Created Inventory table")
        
        # Create Recipes table
        cursor.execute("""
            CREATE TABLE Recipes (
                recipe_id AUTOINCREMENT PRIMARY KEY,
                name TEXT(100) NOT NULL,
                description TEXT(255),
                instructions MEMO,
                yield_quantity DOUBLE DEFAULT 1,
                yield_unit TEXT(20),
                preparation_time INTEGER DEFAULT 0,
                cooking_time INTEGER DEFAULT 0,
                difficulty_level TEXT(20) DEFAULT 'Medium',
                created_by INTEGER,
                created_date DATETIME DEFAULT NOW(),
                last_updated DATETIME DEFAULT NOW(),
                is_active YESNO DEFAULT TRUE,
                FOREIGN KEY (created_by) REFERENCES Users(id)
            )
        """)
        print("Created Recipes table")
        
        # Create RecipeIngredients table (junction table)
        cursor.execute("""
            CREATE TABLE RecipeIngredients (
                id AUTOINCREMENT PRIMARY KEY,
                recipe_id INTEGER NOT NULL,
                item_id INTEGER NOT NULL,
                quantity DOUBLE NOT NULL,
                unit TEXT(20),
                notes TEXT(255),
                FOREIGN KEY (recipe_id) REFERENCES Recipes(recipe_id),
                FOREIGN KEY (item_id) REFERENCES Inventory(item_id)
            )
        """)
        print("Created RecipeIngredients table")
        
        # Create ProductionBatches table
        cursor.execute("""
            CREATE TABLE ProductionBatches (
                batch_id AUTOINCREMENT PRIMARY KEY,
                recipe_id INTEGER NOT NULL,
                batch_number TEXT(50) UNIQUE,
                quantity_produced DOUBLE NOT NULL,
                production_date DATETIME DEFAULT NOW(),
                start_time DATETIME,
                end_time DATETIME,
                status TEXT(20) DEFAULT 'Planned',
                operator_id INTEGER,
                supervisor_id INTEGER,
                notes MEMO,
                cost_total CURRENCY DEFAULT 0,
                FOREIGN KEY (recipe_id) REFERENCES Recipes(recipe_id),
                FOREIGN KEY (operator_id) REFERENCES Users(id),
                FOREIGN KEY (supervisor_id) REFERENCES Users(id)
            )
        """)
        print("Created ProductionBatches table")
        
        # Create TestSamples table
        cursor.execute("""
            CREATE TABLE TestSamples (
                sample_id AUTOINCREMENT PRIMARY KEY,
                batch_id INTEGER NOT NULL,
                sample_number TEXT(50),
                test_type TEXT(50) NOT NULL,
                test_date DATETIME DEFAULT NOW(),
                tested_by INTEGER,
                result TEXT(20) NOT NULL,
                test_values MEMO,
                notes MEMO,
                is_passed YESNO DEFAULT FALSE,
                FOREIGN KEY (batch_id) REFERENCES ProductionBatches(batch_id),
                FOREIGN KEY (tested_by) REFERENCES Users(id)
            )
        """)
        print("Created TestSamples table")
        
        conn.commit()
        print("Database schema created successfully!")
        
        return conn, cursor
        
    except Exception as e:
        print(f"Error creating database schema: {str(e)}")
        raise

def populate_initial_data(cursor, conn):
    """Populate the database with initial data"""
    
    print("Populating initial data...")
    
    # Insert default users
    users_data = [
        ('admin', hash_password('admin123'), 'admin', 'System Administrator', '<EMAIL>'),
        ('manager', hash_password('manager123'), 'manager', 'Production Manager', '<EMAIL>'),
        ('operator1', hash_password('op123'), 'operator', 'Production Operator 1', '<EMAIL>'),
        ('operator2', hash_password('op123'), 'operator', 'Production Operator 2', '<EMAIL>'),
        ('qc_tech', hash_password('qc123'), 'qc_technician', 'Quality Control Technician', '<EMAIL>')
    ]
    
    for user_data in users_data:
        cursor.execute("""
            INSERT INTO Users (username, password_hash, role, full_name, email)
            VALUES (?, ?, ?, ?, ?)
        """, user_data)
    
    print("Inserted default users")
    
    # Insert sample inventory items
    inventory_data = [
        ('Flour', 'All-purpose flour', 100.0, 'kg', 20.0, 2.50, 'ABC Suppliers', 'Raw Materials'),
        ('Sugar', 'Granulated white sugar', 50.0, 'kg', 10.0, 1.80, 'Sweet Co.', 'Raw Materials'),
        ('Eggs', 'Fresh chicken eggs', 200.0, 'pieces', 50.0, 0.25, 'Farm Fresh', 'Raw Materials'),
        ('Butter', 'Unsalted butter', 25.0, 'kg', 5.0, 8.50, 'Dairy Plus', 'Raw Materials'),
        ('Vanilla Extract', 'Pure vanilla extract', 2.0, 'liters', 0.5, 45.00, 'Flavor House', 'Flavorings'),
        ('Baking Powder', 'Double-acting baking powder', 5.0, 'kg', 1.0, 12.00, 'Chem Supply', 'Leavening'),
        ('Salt', 'Fine table salt', 10.0, 'kg', 2.0, 1.20, 'Salt Works', 'Seasonings')
    ]
    
    for item_data in inventory_data:
        cursor.execute("""
            INSERT INTO Inventory (name, description, quantity, unit, minimum_stock, cost_per_unit, supplier, category)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, item_data)
    
    print("Inserted sample inventory items")
    
    # Insert sample recipes
    recipes_data = [
        ('Basic Vanilla Cake', 'Classic vanilla sponge cake', 
         'Mix dry ingredients, cream butter and sugar, add eggs and vanilla, alternate flour and milk', 
         1.0, 'cake', 30, 45, 'Medium', 1),
        ('Chocolate Chip Cookies', 'Soft and chewy chocolate chip cookies',
         'Cream butter and sugars, add eggs and vanilla, mix in flour and chocolate chips',
         24.0, 'pieces', 20, 12, 'Easy', 1)
    ]
    
    for recipe_data in recipes_data:
        cursor.execute("""
            INSERT INTO Recipes (name, description, instructions, yield_quantity, yield_unit, 
                               preparation_time, cooking_time, difficulty_level, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, recipe_data)
    
    print("Inserted sample recipes")
    
    # Insert recipe ingredients
    recipe_ingredients_data = [
        # Vanilla Cake ingredients (recipe_id = 1)
        (1, 1, 2.5, 'kg', 'Sifted'),  # Flour
        (1, 2, 1.5, 'kg', ''),        # Sugar
        (1, 3, 8.0, 'pieces', 'Room temperature'),  # Eggs
        (1, 4, 0.5, 'kg', 'Softened'),  # Butter
        (1, 5, 0.02, 'liters', ''),   # Vanilla
        (1, 6, 0.05, 'kg', ''),       # Baking Powder
        (1, 7, 0.01, 'kg', ''),       # Salt
        
        # Chocolate Chip Cookies ingredients (recipe_id = 2)
        (2, 1, 1.0, 'kg', ''),        # Flour
        (2, 2, 0.5, 'kg', ''),        # Sugar
        (2, 3, 2.0, 'pieces', ''),    # Eggs
        (2, 4, 0.25, 'kg', ''),       # Butter
        (2, 5, 0.01, 'liters', ''),   # Vanilla
    ]
    
    for ingredient_data in recipe_ingredients_data:
        cursor.execute("""
            INSERT INTO RecipeIngredients (recipe_id, item_id, quantity, unit, notes)
            VALUES (?, ?, ?, ?, ?)
        """, ingredient_data)
    
    print("Inserted recipe ingredients")
    
    conn.commit()
    print("Initial data populated successfully!")

def main():
    """Main function to create and initialize the database"""
    
    # Database path
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    db_path = os.path.join(base_dir, 'db', 'mydb.accdb')
    
    print(f"Setting up database at: {db_path}")
    
    try:
        # Create schema
        conn, cursor = create_database_schema(db_path)
        
        # Populate initial data
        populate_initial_data(cursor, conn)
        
        # Close connection
        conn.close()
        
        print("\nDatabase setup completed successfully!")
        print("Default users created:")
        print("- admin/admin123 (Administrator)")
        print("- manager/manager123 (Manager)")
        print("- operator1/op123 (Operator)")
        print("- operator2/op123 (Operator)")
        print("- qc_tech/qc123 (QC Technician)")
        
    except Exception as e:
        print(f"Error setting up database: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()
