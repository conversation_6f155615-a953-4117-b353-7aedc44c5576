# Authentication and User Management System

This project is a PyQt5-based application for managing users, inventory, recipes, production, quality assurance, and reporting, using a Microsoft Access database.

## Features
- User authentication and permissions management
- Inventory and recipe management
- Production and quality assurance modules
- Integrated reporting with print and PDF export
- Data encryption and protection

## Structure
- `/models`: Data models and classes
- `/ui`: PyQt5 UI files
- `/db`: Database access and management
- `/reports`: Report templates and logic
- `/utils`: Utility tools and services

## Getting Started
1. Install dependencies: `pip install -r requirements.txt`
2. Run the app: `python main.py`

---
