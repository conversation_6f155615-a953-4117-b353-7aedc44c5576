import pyodbc
import os
import threading
import time
from contextlib import contextmanager
from datetime import datetime, timedelta

class DatabaseManager:
    """Enhanced database manager with connection pooling and error handling"""

    def __init__(self, db_path, max_connections=5):
        self.db_path = os.path.abspath(db_path)
        self.max_connections = max_connections
        self.connections = []
        self.lock = threading.Lock()
        self._connection_string = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path}'

    def _create_connection(self):
        """Create a new database connection"""
        try:
            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"Database file not found at: {self.db_path}")

            conn = pyodbc.connect(self._connection_string)
            conn.autocommit = False  # Enable transaction control
            return conn
        except pyodbc.Error as e:
            print(f"ODBC Error: {str(e)}")
            raise
        except Exception as e:
            print(f"Database connection error: {str(e)}")
            raise

    def get_connection(self):
        """Get a connection from the pool or create a new one"""
        with self.lock:
            if self.connections:
                return self.connections.pop()
            else:
                return self._create_connection()

    def return_connection(self, conn):
        """Return a connection to the pool"""
        with self.lock:
            if len(self.connections) < self.max_connections:
                try:
                    # Test if connection is still valid
                    conn.execute("SELECT 1")
                    self.connections.append(conn)
                except:
                    # Connection is invalid, close it
                    try:
                        conn.close()
                    except:
                        pass
            else:
                # Pool is full, close the connection
                try:
                    conn.close()
                except:
                    pass

    @contextmanager
    def get_db_connection(self):
        """Context manager for database connections"""
        conn = None
        try:
            conn = self.get_connection()
            yield conn
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise
        finally:
            if conn:
                self.return_connection(conn)

    def execute_query(self, query, params=None, fetch=False):
        """Execute a query with automatic connection management"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                if fetch:
                    if fetch == 'one':
                        result = cursor.fetchone()
                    elif fetch == 'all':
                        result = cursor.fetchall()
                    else:
                        result = cursor.fetchmany(fetch)
                else:
                    result = cursor.rowcount

                conn.commit()
                return result
            except Exception as e:
                conn.rollback()
                raise
            finally:
                cursor.close()

    def close_all_connections(self):
        """Close all connections in the pool"""
        with self.lock:
            for conn in self.connections:
                try:
                    conn.close()
                except:
                    pass
            self.connections.clear()

class Database:
    """Legacy Database class for backward compatibility"""

    def __init__(self, db_path):
        self.db_path = db_path
        self.conn = None

    def connect(self):
        try:
            # Get absolute path
            abs_path = os.path.abspath(self.db_path)
            if not os.path.exists(abs_path):
                raise FileNotFoundError(f"Database file not found at: {abs_path}")

            connection_string = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={abs_path}'
            print(f"Attempting to connect with: {connection_string}")
            self.conn = pyodbc.connect(connection_string)
            return self.conn
        except pyodbc.Error as e:
            print(f"ODBC Error: {str(e)}")
            raise
        except Exception as e:
            print(f"General Error: {str(e)}")
            raise

    def close(self):
        if self.conn:
            self.conn.close()

# Global database manager instance
_db_manager = None

def get_db_manager(db_path=None):
    """Get the global database manager instance"""
    global _db_manager
    if _db_manager is None and db_path:
        _db_manager = DatabaseManager(db_path)
    return _db_manager

# Usage:
# db = Database('db/mydb.accdb')  # Legacy usage
# conn = db.connect()
#
# Or use the new manager:
# db_manager = get_db_manager('db/mydb.accdb')
# with db_manager.get_db_connection() as conn:
#     cursor = conn.cursor()
#     cursor.execute("SELECT * FROM Users")
#     results = cursor.fetchall()
