import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db.database import Database
from utils.config import DB_PATH

if __name__ == '__main__':
    db = Database(DB_PATH)
    try:
        conn = db.connect()
        print('Database connection successful!')
        db.close()
    except Exception as e:
        print(f'Failed to connect to database: {e}')
