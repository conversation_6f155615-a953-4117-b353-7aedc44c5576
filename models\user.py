import hashlib
import secrets
import uuid
from datetime import datetime, timedelta
from cryptography.fernet import <PERSON><PERSON><PERSON>
from db.database import get_db_manager
from utils.config import DB_PATH, USER_ROLES, SESSION_TIMEOUT

class User:
    """Enhanced User class with comprehensive authentication and authorization"""

    def __init__(self, user_id=None, username=None, password_hash=None, role=None,
                 full_name=None, email=None, created_date=None, last_login=None, is_active=True):
        self.user_id = user_id
        self.username = username
        self.password_hash = password_hash
        self.role = role
        self.full_name = full_name
        self.email = email
        self.created_date = created_date
        self.last_login = last_login
        self.is_active = is_active
        self._permissions = None

    @staticmethod
    def hash_password(password, salt=None):
        """Hash password with salt using SHA-256"""
        if salt is None:
            salt = secrets.token_hex(16)

        # Combine password and salt
        password_salt = password + salt
        hash_obj = hashlib.sha256(password_salt.encode())
        password_hash = salt + hash_obj.hexdigest()

        return password_hash

    def check_password(self, password):
        """Verify password against stored hash"""
        if not self.password_hash or len(self.password_hash) < 32:
            return False

        # Extract salt from stored hash
        salt = self.password_hash[:32]
        stored_hash = self.password_hash[32:]

        # Hash the provided password with the same salt
        password_salt = password + salt
        hash_obj = hashlib.sha256(password_salt.encode())
        provided_hash = hash_obj.hexdigest()

        return provided_hash == stored_hash

    def set_password(self, password):
        """Set a new password for the user"""
        self.password_hash = self.hash_password(password)

    @property
    def permissions(self):
        """Get user permissions based on role"""
        if self._permissions is None:
            role_info = USER_ROLES.get(self.role, {})
            self._permissions = role_info.get('permissions', [])
        return self._permissions

    def has_permission(self, permission):
        """Check if user has a specific permission"""
        return 'all' in self.permissions or permission in self.permissions

    def get_role_name(self):
        """Get the display name for the user's role"""
        role_info = USER_ROLES.get(self.role, {})
        return role_info.get('name', self.role.title())

    def update_last_login(self):
        """Update the last login timestamp"""
        self.last_login = datetime.now()

        # Update in database
        try:
            db_manager = get_db_manager(DB_PATH)
            db_manager.execute_query(
                "UPDATE Users SET last_login = ? WHERE id = ?",
                (self.last_login, self.user_id)
            )
        except Exception as e:
            print(f"Error updating last login: {str(e)}")

    def to_dict(self):
        """Convert user object to dictionary"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'role': self.role,
            'full_name': self.full_name,
            'email': self.email,
            'created_date': self.created_date,
            'last_login': self.last_login,
            'is_active': self.is_active,
            'role_name': self.get_role_name(),
            'permissions': self.permissions
        }

    @classmethod
    def from_db_row(cls, row):
        """Create User object from database row"""
        return cls(
            user_id=row[0],
            username=row[1],
            password_hash=row[2],
            role=row[3],
            full_name=row[4] if len(row) > 4 else None,
            email=row[5] if len(row) > 5 else None,
            created_date=row[6] if len(row) > 6 else None,
            last_login=row[7] if len(row) > 7 else None,
            is_active=row[8] if len(row) > 8 else True
        )

    @classmethod
    def authenticate(cls, username, password):
        """Authenticate user with username and password"""
        try:
            db_manager = get_db_manager(DB_PATH)

            # Get user from database
            result = db_manager.execute_query(
                """SELECT id, username, password_hash, role, full_name, email,
                          created_date, last_login, is_active
                   FROM Users WHERE username = ? AND is_active = True""",
                (username,),
                fetch='one'
            )

            if not result:
                return None

            user = cls.from_db_row(result)

            # Check password
            if user.check_password(password):
                user.update_last_login()
                return user
            else:
                return None

        except Exception as e:
            print(f"Authentication error: {str(e)}")
            return None

    @classmethod
    def get_by_id(cls, user_id):
        """Get user by ID"""
        try:
            db_manager = get_db_manager(DB_PATH)

            result = db_manager.execute_query(
                """SELECT id, username, password_hash, role, full_name, email,
                          created_date, last_login, is_active
                   FROM Users WHERE id = ?""",
                (user_id,),
                fetch='one'
            )

            if result:
                return cls.from_db_row(result)
            else:
                return None

        except Exception as e:
            print(f"Error getting user by ID: {str(e)}")
            return None

    @classmethod
    def get_all_users(cls):
        """Get all users"""
        try:
            db_manager = get_db_manager(DB_PATH)

            results = db_manager.execute_query(
                """SELECT id, username, password_hash, role, full_name, email,
                          created_date, last_login, is_active
                   FROM Users ORDER BY username""",
                fetch='all'
            )

            return [cls.from_db_row(row) for row in results]

        except Exception as e:
            print(f"Error getting all users: {str(e)}")
            return []

    def save(self):
        """Save user to database"""
        try:
            db_manager = get_db_manager(DB_PATH)

            if self.user_id:
                # Update existing user
                db_manager.execute_query(
                    """UPDATE Users SET username = ?, password_hash = ?, role = ?,
                              full_name = ?, email = ?, is_active = ?
                       WHERE id = ?""",
                    (self.username, self.password_hash, self.role, self.full_name,
                     self.email, self.is_active, self.user_id)
                )
            else:
                # Insert new user
                result = db_manager.execute_query(
                    """INSERT INTO Users (username, password_hash, role, full_name, email, is_active)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (self.username, self.password_hash, self.role, self.full_name,
                     self.email, self.is_active),
                    fetch='one'
                )
                # Note: Access doesn't return the inserted ID directly, need to query for it
                user_result = db_manager.execute_query(
                    "SELECT id FROM Users WHERE username = ?",
                    (self.username,),
                    fetch='one'
                )
                if user_result:
                    self.user_id = user_result[0]

            return True

        except Exception as e:
            print(f"Error saving user: {str(e)}")
            return False

    def delete(self):
        """Soft delete user (set is_active to False)"""
        try:
            self.is_active = False
            return self.save()
        except Exception as e:
            print(f"Error deleting user: {str(e)}")
            return False
