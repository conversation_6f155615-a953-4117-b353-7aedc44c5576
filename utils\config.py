import os
from cryptography.fernet import Fernet

# Get the absolute path to the database file
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DB_PATH = os.path.join(BASE_DIR, 'db', 'mydb.accdb')

# Generate or load encryption key
ENCRYPTION_KEY_FILE = os.path.join(BASE_DIR, 'utils', 'encryption.key')

def get_encryption_key():
    """Get or generate encryption key"""
    if os.path.exists(ENCRYPTION_KEY_FILE):
        with open(ENCRYPTION_KEY_FILE, 'rb') as key_file:
            return key_file.read()
    else:
        # Generate new key
        key = Fernet.generate_key()
        with open(ENCRYPTION_KEY_FILE, 'wb') as key_file:
            key_file.write(key)
        return key

ENCRYPTION_KEY = get_encryption_key()

# Application settings
APP_NAME = "Authentication and User Management System"
APP_VERSION = "1.0.0"

# User roles and permissions
USER_ROLES = {
    'admin': {
        'name': 'Administrator',
        'permissions': ['all']
    },
    'manager': {
        'name': 'Manager',
        'permissions': ['user_management', 'inventory', 'recipes', 'production', 'quality', 'reports']
    },
    'operator': {
        'name': 'Operator',
        'permissions': ['inventory', 'production', 'quality']
    },
    'qc_technician': {
        'name': 'Quality Control Technician',
        'permissions': ['quality', 'reports']
    }
}

# Database settings
DB_TIMEOUT = 30  # seconds
DB_MAX_CONNECTIONS = 5

# Session settings
SESSION_TIMEOUT = 3600  # 1 hour in seconds
SESSION_CLEANUP_INTERVAL = 300  # 5 minutes in seconds

# UI settings
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
SIDEBAR_WIDTH = 250
