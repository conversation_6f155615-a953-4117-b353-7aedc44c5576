"""
Session management for user authentication and authorization.
Handles user sessions, session timeouts, and session cleanup.
"""

import secrets
import uuid
from datetime import datetime, timedelta
from db.database import get_db_manager
from utils.config import DB_PATH, SESSION_TIMEOUT

class UserSession:
    """User session management class"""
    
    def __init__(self, session_id=None, user_id=None, created_at=None, 
                 expires_at=None, is_active=True):
        self.session_id = session_id
        self.user_id = user_id
        self.created_at = created_at or datetime.now()
        self.expires_at = expires_at or (datetime.now() + timedelta(seconds=SESSION_TIMEOUT))
        self.is_active = is_active

    @classmethod
    def create_session(cls, user_id):
        """Create a new session for a user"""
        try:
            session_id = secrets.token_urlsafe(32)
            created_at = datetime.now()
            expires_at = created_at + timedelta(seconds=SESSION_TIMEOUT)
            
            db_manager = get_db_manager(DB_PATH)
            
            # Insert session into database
            db_manager.execute_query(
                """INSERT INTO UserSessions (session_id, user_id, created_at, expires_at, is_active)
                   VALUES (?, ?, ?, ?, ?)""",
                (session_id, user_id, created_at, expires_at, True)
            )
            
            return cls(session_id, user_id, created_at, expires_at, True)
            
        except Exception as e:
            print(f"Error creating session: {str(e)}")
            return None

    @classmethod
    def get_session(cls, session_id):
        """Get session by session ID"""
        try:
            db_manager = get_db_manager(DB_PATH)
            
            result = db_manager.execute_query(
                """SELECT session_id, user_id, created_at, expires_at, is_active
                   FROM UserSessions 
                   WHERE session_id = ? AND is_active = True""",
                (session_id,),
                fetch='one'
            )
            
            if result:
                return cls(*result)
            else:
                return None
                
        except Exception as e:
            print(f"Error getting session: {str(e)}")
            return None

    def is_valid(self):
        """Check if session is valid (not expired and active)"""
        if not self.is_active:
            return False
        
        if datetime.now() > self.expires_at:
            self.invalidate()
            return False
        
        return True

    def extend_session(self, additional_seconds=None):
        """Extend session expiration time"""
        if additional_seconds is None:
            additional_seconds = SESSION_TIMEOUT
        
        try:
            self.expires_at = datetime.now() + timedelta(seconds=additional_seconds)
            
            db_manager = get_db_manager(DB_PATH)
            db_manager.execute_query(
                "UPDATE UserSessions SET expires_at = ? WHERE session_id = ?",
                (self.expires_at, self.session_id)
            )
            
            return True
            
        except Exception as e:
            print(f"Error extending session: {str(e)}")
            return False

    def invalidate(self):
        """Invalidate the session"""
        try:
            self.is_active = False
            
            db_manager = get_db_manager(DB_PATH)
            db_manager.execute_query(
                "UPDATE UserSessions SET is_active = False WHERE session_id = ?",
                (self.session_id,)
            )
            
            return True
            
        except Exception as e:
            print(f"Error invalidating session: {str(e)}")
            return False

    @classmethod
    def cleanup_expired_sessions(cls):
        """Clean up expired sessions from database"""
        try:
            db_manager = get_db_manager(DB_PATH)
            
            # Mark expired sessions as inactive
            result = db_manager.execute_query(
                "UPDATE UserSessions SET is_active = False WHERE expires_at < ? AND is_active = True",
                (datetime.now(),)
            )
            
            print(f"Cleaned up {result} expired sessions")
            return result
            
        except Exception as e:
            print(f"Error cleaning up sessions: {str(e)}")
            return 0

    @classmethod
    def get_user_sessions(cls, user_id):
        """Get all active sessions for a user"""
        try:
            db_manager = get_db_manager(DB_PATH)
            
            results = db_manager.execute_query(
                """SELECT session_id, user_id, created_at, expires_at, is_active
                   FROM UserSessions 
                   WHERE user_id = ? AND is_active = True
                   ORDER BY created_at DESC""",
                (user_id,),
                fetch='all'
            )
            
            return [cls(*row) for row in results]
            
        except Exception as e:
            print(f"Error getting user sessions: {str(e)}")
            return []

    @classmethod
    def invalidate_user_sessions(cls, user_id, exclude_session_id=None):
        """Invalidate all sessions for a user (except optionally one)"""
        try:
            db_manager = get_db_manager(DB_PATH)
            
            if exclude_session_id:
                result = db_manager.execute_query(
                    """UPDATE UserSessions SET is_active = False 
                       WHERE user_id = ? AND session_id != ? AND is_active = True""",
                    (user_id, exclude_session_id)
                )
            else:
                result = db_manager.execute_query(
                    "UPDATE UserSessions SET is_active = False WHERE user_id = ? AND is_active = True",
                    (user_id,)
                )
            
            return result
            
        except Exception as e:
            print(f"Error invalidating user sessions: {str(e)}")
            return 0

    def to_dict(self):
        """Convert session to dictionary"""
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'created_at': self.created_at,
            'expires_at': self.expires_at,
            'is_active': self.is_active,
            'is_valid': self.is_valid()
        }

class SessionManager:
    """Global session manager for the application"""
    
    def __init__(self):
        self.current_session = None
        self.current_user = None

    def login(self, user):
        """Login user and create session"""
        from models.user import User
        
        try:
            # Invalidate any existing sessions for this user (single session per user)
            UserSession.invalidate_user_sessions(user.user_id)
            
            # Create new session
            session = UserSession.create_session(user.user_id)
            
            if session:
                self.current_session = session
                self.current_user = user
                return True
            else:
                return False
                
        except Exception as e:
            print(f"Login error: {str(e)}")
            return False

    def logout(self):
        """Logout current user and invalidate session"""
        try:
            if self.current_session:
                self.current_session.invalidate()
            
            self.current_session = None
            self.current_user = None
            return True
            
        except Exception as e:
            print(f"Logout error: {str(e)}")
            return False

    def is_logged_in(self):
        """Check if user is currently logged in"""
        if not self.current_session or not self.current_user:
            return False
        
        return self.current_session.is_valid()

    def extend_session(self):
        """Extend current session"""
        if self.current_session:
            return self.current_session.extend_session()
        return False

    def get_current_user(self):
        """Get current logged in user"""
        if self.is_logged_in():
            return self.current_user
        return None

    def has_permission(self, permission):
        """Check if current user has permission"""
        if not self.is_logged_in():
            return False
        
        return self.current_user.has_permission(permission)

    def require_permission(self, permission):
        """Decorator to require specific permission"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                if not self.has_permission(permission):
                    raise PermissionError(f"Permission '{permission}' required")
                return func(*args, **kwargs)
            return wrapper
        return decorator

# Global session manager instance
session_manager = SessionManager()
