from PyQt5.QtWidgets import <PERSON>Dialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox

class LoginDialog(QDialog):
    def __init__(self, users):
        super().__init__()
        self.setWindowTitle('Login')
        self.users = users
        layout = QVBoxLayout()
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('Username')
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('Password')
        self.password_input.setEchoMode(QLineEdit.Password)
        self.login_button = QPushButton('Login')
        self.login_button.clicked.connect(self.handle_login)
        layout.addWidget(QLabel('Username:'))
        layout.addWidget(self.username_input)
        layout.addWidget(QLabel('Password:'))
        layout.addWidget(self.password_input)
        layout.addWidget(self.login_button)
        self.setLayout(layout)
        self.logged_user = None

    def handle_login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        user = next((u for u in self.users if u[1] == username and u[2] == password), None)
        if user:
            self.logged_user = user
            self.accept()
        else:
            QMessageBox.warning(self, 'Error', 'Invalid username or password')
