"""
Database initialization script that checks if database exists and creates it if needed.
This script is called automatically when the application starts.
"""

import os
import sys
from create_database import main as create_db
from database import get_db_manager
from utils.config import DB_PATH

def check_database_exists():
    """Check if the database file exists"""
    return os.path.exists(DB_PATH)

def check_database_schema():
    """Check if the database has the required tables"""
    try:
        db_manager = get_db_manager(DB_PATH)
        
        # List of required tables
        required_tables = [
            'Users', 'UserSessions', 'Inventory', 'Recipes', 
            'RecipeIngredients', 'ProductionBatches', 'TestSamples'
        ]
        
        with db_manager.get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Get list of existing tables
            cursor.execute("""
                SELECT Name FROM MSysObjects 
                WHERE Type=1 AND Flags=0
            """)
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            # Check if all required tables exist
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if missing_tables:
                print(f"Missing tables: {missing_tables}")
                return False
            
            return True
            
    except Exception as e:
        print(f"Error checking database schema: {str(e)}")
        return False

def initialize_database():
    """Initialize the database if needed"""
    
    print("Checking database initialization...")
    
    # Check if database file exists
    if not check_database_exists():
        print("Database file not found. Creating new database...")
        try:
            success = create_db()
            if success:
                print("Database created successfully!")
                return True
            else:
                print("Failed to create database!")
                return False
        except Exception as e:
            print(f"Error creating database: {str(e)}")
            return False
    
    # Check if database has required schema
    if not check_database_schema():
        print("Database schema incomplete. Recreating database...")
        try:
            # Backup existing database if it exists
            if os.path.exists(DB_PATH):
                backup_path = DB_PATH + '.backup'
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                os.rename(DB_PATH, backup_path)
                print(f"Existing database backed up to: {backup_path}")
            
            success = create_db()
            if success:
                print("Database schema created successfully!")
                return True
            else:
                print("Failed to create database schema!")
                return False
        except Exception as e:
            print(f"Error creating database schema: {str(e)}")
            return False
    
    print("Database is properly initialized.")
    return True

def test_database_connection():
    """Test the database connection and basic operations"""
    try:
        db_manager = get_db_manager(DB_PATH)
        
        # Test basic query
        result = db_manager.execute_query("SELECT COUNT(*) FROM Users", fetch='one')
        user_count = result[0] if result else 0
        
        print(f"Database connection test successful. Found {user_count} users.")
        return True
        
    except Exception as e:
        print(f"Database connection test failed: {str(e)}")
        return False

if __name__ == "__main__":
    """Run database initialization when script is executed directly"""
    
    print("Starting database initialization...")
    
    # Initialize database
    if initialize_database():
        # Test connection
        if test_database_connection():
            print("Database initialization completed successfully!")
            sys.exit(0)
        else:
            print("Database initialization failed - connection test failed!")
            sys.exit(1)
    else:
        print("Database initialization failed!")
        sys.exit(1)
